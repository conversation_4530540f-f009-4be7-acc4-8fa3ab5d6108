import {useState, useCallback} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

interface UseDynamicAdHeightReturn {
  adHeight: number;
  contentPaddingBottom: number;
  handleAdHeightChange: (height: number) => void;
}

/**
 * Custom hook to manage dynamic native ad height and content padding
 */
export const useDynamicAdHeight = (): UseDynamicAdHeightReturn => {
  const insets = useSafeAreaInsets();
  const [adHeight, setAdHeight] = useState(0);

  const handleAdHeightChange = useCallback((height: number) => {
    console.log('[useDynamicAdHeight] Ad height changed to:', height);
    setAdHeight(height);
  }, []);

  // Calculate content padding based on ad height and safe area
  const contentPaddingBottom = Math.max(
    insets.bottom + adHeight + 20, // 20px extra spacing
    20, // Minimum padding
  );

  return {
    adHeight,
    contentPaddingBottom,
    handleAdHeightChange,
  };
};
