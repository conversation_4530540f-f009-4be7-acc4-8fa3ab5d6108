import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Image,
} from 'react-native';
import {debounce} from 'lodash';
import {
  requestMicrophonePermission,
  isVoiceRecognitionAvailable,
} from '../services/VoiceRecognitionService';
import {useVoiceRecognition} from '../contexts/VoiceRecognitionContext';
import {Images} from '../assets';

interface CustomSearchBarProps {
  onSearch: (query: string) => void;
  onVoiceResult?: (query: string) => void;
  placeholder?: string;
  initialValue?: string;
  isSearching?: boolean;
  debounceTime?: number;
  showVoiceSearch?: boolean;
  screenName?: string;
  isScreenFocused?: boolean;
}

const CustomSearchBar = ({
  onSearch,
  onVoiceResult,
  placeholder = 'Search',
  initialValue = '',
  isSearching = false,
  debounceTime = 300,
  showVoiceSearch = true,
  screenName = 'unknown',
  isScreenFocused = true,
}: CustomSearchBarProps) => {
  const [searchText, setSearchText] = useState(initialValue);
  const [voiceAvailable, setVoiceAvailable] = useState(false);
  const [listeningTimeoutId, setListeningTimeoutId] =
    useState<NodeJS.Timeout | null>(null);

  // Use the voice recognition context
  const {
    registerScreen,
    unregisterScreen,
    startVoiceRecognition,
    stopVoiceRecognition,
    isListening,
  } = useVoiceRecognition();

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((text: string) => {
      onSearch(text);
    }, debounceTime),
    [onSearch, debounceTime],
  );

  // Use refs to capture latest values for voice handlers
  const onVoiceResultRef = useRef(onVoiceResult);
  const onSearchRef = useRef(onSearch);
  const debouncedSearchRef = useRef(debouncedSearch);

  // Update refs when props change
  useEffect(() => {
    onVoiceResultRef.current = onVoiceResult;
    onSearchRef.current = onSearch;
    debouncedSearchRef.current = debouncedSearch;
  });

  // Check voice availability
  useEffect(() => {
    try {
      const available = isVoiceRecognitionAvailable();
      console.log('Voice recognition available:', available);
      setVoiceAvailable(available);
    } catch (error) {
      console.error('Error checking voice availability:', error);
      setVoiceAvailable(false);
    }
  }, []);

  // Register/unregister screen with voice recognition context
  useEffect(() => {
    if (!voiceAvailable || !showVoiceSearch || !isScreenFocused) {
      console.log(
        `[${screenName}] Skipping voice registration - voiceAvailable: ${voiceAvailable}, showVoiceSearch: ${showVoiceSearch}, isScreenFocused: ${isScreenFocused}`,
      );
      return;
    }

    console.log(`[${screenName}] Registering with voice recognition manager`);

    // Register this screen with the voice recognition manager
    registerScreen(
      screenName,
      () => {
        console.log(`[${screenName}] Speech started`);
        // Clear any existing timeout
        if (listeningTimeoutId) {
          clearTimeout(listeningTimeoutId);
          setListeningTimeoutId(null);
        }
      },
      () => {
        console.log(`[${screenName}] Speech ended`);
        // Clear the local timeout when speech ends
        if (listeningTimeoutId) {
          clearTimeout(listeningTimeoutId);
          setListeningTimeoutId(null);
        }
      },
      (results: string[]) => {
        console.log(`[${screenName}] Speech results:`, results);

        if (results && results.length > 0) {
          const recognizedText = results[0];
          console.log(
            `[${screenName}] Setting search text to:`,
            recognizedText,
          );
          setSearchText(recognizedText);
          debouncedSearchRef.current.cancel(); // Cancel any pending debounced searches

          // Only call onVoiceResult for voice input, not onSearch to avoid duplicate calls
          if (onVoiceResultRef.current) {
            console.log(
              `[${screenName}] Calling onVoiceResult with:`,
              recognizedText,
            );
            onVoiceResultRef.current(recognizedText);
          } else {
            // Fallback to onSearch if onVoiceResult is not provided
            console.log(
              `[${screenName}] Calling onSearch with:`,
              recognizedText,
            );
            onSearchRef.current(recognizedText);
          }
        }
      },
      (error: any) => {
        console.log(`[${screenName}] Speech error:`, error);

        // Clear the local timeout when there's an error
        if (listeningTimeoutId) {
          clearTimeout(listeningTimeoutId);
          setListeningTimeoutId(null);
        }

        // Handle different types of errors
        if (error && error.isRecoverable) {
          console.log('Recoverable speech error:', error.message);
        } else {
          console.error('Non-recoverable speech error:', error);
        }
      },
    );

    // Cleanup function
    return () => {
      console.log(
        `[${screenName}] Unregistering from voice recognition manager`,
      );
      unregisterScreen(screenName);
    };
  }, [voiceAvailable, showVoiceSearch, isScreenFocused, screenName]);

  // Update searchText when initialValue changes
  useEffect(() => {
    setSearchText(initialValue);
  }, [initialValue]);

  // Handle text input changes
  const handleTextChange = (text: string) => {
    setSearchText(text);
    debouncedSearch(text);
  };

  // Clear search text
  const clearSearch = () => {
    setSearchText('');
    onSearch('');
  };

  // Handle voice search
  const handleVoiceSearch = async () => {
    if (isListening) {
      console.log(`[${screenName}] Stopping voice recognition`);
      await stopVoiceRecognition();
      return;
    }

    try {
      // Request microphone permission
      const hasPermission = await requestMicrophonePermission();
      if (!hasPermission) {
        console.log('Microphone permission denied');
        return;
      }

      console.log(`[${screenName}] Starting voice recognition`);
      const started = await startVoiceRecognition();

      if (started) {
        // Set a timeout to automatically stop listening after 10 seconds
        const timeoutId = setTimeout(async () => {
          console.log('Local voice recognition timeout');
          await stopVoiceRecognition();
          setListeningTimeoutId(null);
        }, 10000);

        setListeningTimeoutId(timeoutId);
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
    }
  };

  return (
    <>
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Image source={Images.ic_magnifyingGlass} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder={placeholder}
            placeholderTextColor="#999"
            value={searchText}
            onChangeText={handleTextChange}
            autoCapitalize="none"
            autoCorrect={false}
            autoComplete="off"
            spellCheck={false}
            textContentType="none"
            keyboardType="default"
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
              <Image source={Images.ic_close} style={styles.clearIcon} />
            </TouchableOpacity>
          )}
        </View>

        {showVoiceSearch && voiceAvailable && (
          <TouchableOpacity
            style={[
              styles.voiceButton,
              isListening && styles.voiceButtonActive,
            ]}
            onPress={handleVoiceSearch}
            disabled={!isScreenFocused}>
            {isListening ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Image
                source={Images.ic_microphone}
                style={[styles.micIcon, {opacity: isScreenFocused ? 1 : 0.5}]}
              />
            )}
          </TouchableOpacity>
        )}
      </View>

      {isSearching && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f5f9',
    borderRadius: 25,
    paddingHorizontal: 15,
    height: 45,
    borderWidth: 1,
    borderColor: '#D7E2F1',
  },
  searchIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
    tintColor: '#666',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 0,
  },
  clearButton: {
    padding: 5,
  },
  clearIcon: {
    width: 20,
    height: 20,
    tintColor: '#666',
  },
  micIcon: {
    width: 20,
    height: 20,
    tintColor: '#fff',
  },
  voiceButton: {
    backgroundColor: '#007AFF',
    borderRadius: 22,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  voiceButtonActive: {
    backgroundColor: '#FF3B30',
  },
  loadingContainer: {
    paddingVertical: 10,
    alignItems: 'center',
  },
});

export default CustomSearchBar;
