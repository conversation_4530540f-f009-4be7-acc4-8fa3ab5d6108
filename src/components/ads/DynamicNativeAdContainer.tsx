import React, {useState, useCallback} from 'react';
import {View, StyleSheet} from 'react-native';
import {NativeAd} from 'react-native-google-mobile-ads';
import CustomNativeAdCard from './CustomNativeAdCard';

interface Props {
  ad: NativeAd | null;
  onHeightChange?: (height: number) => void;
  children?: React.ReactNode;
}

const DynamicNativeAdContainer: React.FC<Props> = ({
  ad,
  onHeightChange,
  children,
}) => {
  const [adHeight, setAdHeight] = useState(0);

  const handleAdLayout = useCallback(
    (event: any) => {
      const {height} = event.nativeEvent.layout;
      const totalHeight = height + 30; // Add padding/margin
      
      console.log('[DynamicNativeAd] Measured height:', totalHeight);
      
      if (totalHeight !== adHeight) {
        setAdHeight(totalHeight);
        onHeightChange?.(totalHeight);
      }
    },
    [adHeight, onHeightChange],
  );

  if (!ad) {
    // No ad - notify parent that height is 0
    if (adHeight !== 0) {
      setAdHeight(0);
      onHeightChange?.(0);
    }
    return <>{children}</>;
  }

  return (
    <>
      {children}
      <View style={styles.nativeAdContainer} onLayout={handleAdLayout}>
        <CustomNativeAdCard ad={ad} />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  nativeAdContainer: {
    position: 'absolute',
    bottom: 15,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
});

export default DynamicNativeAdContainer;
