import React, {useState, useEffect} from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Image,
  FlatList,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {Text, Button} from 'react-native-paper';
import {Images} from '../../assets';
import {COLORS, FONTS} from '../../common/constant';
import commonStyles from '../../common/commonStyles';
import noteRepository, {
  NoteData,
} from '../../database/watermelon/repositories/noteRepository';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import pushNotificationService from '../../services/pushNotificationService';

interface CustomModalProps {
  visible: boolean;
  title: string;
  content: string;
  companyId: string; // Add companyId prop
  onClose: () => void;
}

const NoteModalScreen = ({
  visible,
  title,
  content,
  companyId,
  onClose,
}: CustomModalProps) => {
  const [notesList, setNotesList] = useState<NoteData[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [reminderAt, setReminderAt] = useState<Date | null>(null);
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  // New state for individual note reminders
  const [selectedNoteForReminder, setSelectedNoteForReminder] =
    useState<NoteData | null>(null);
  const [isIndividualDatePickerVisible, setIndividualDatePickerVisibility] =
    useState(false);

  const showDateTimePicker = () => setDatePickerVisibility(true);
  const hideDateTimePicker = () => setDatePickerVisibility(false);

  // Helper functions for individual note reminders
  const showIndividualDateTimePicker = (note: NoteData) => {
    setSelectedNoteForReminder(note);
    setIndividualDatePickerVisibility(true);
  };

  const hideIndividualDateTimePicker = () => {
    setSelectedNoteForReminder(null);
    setIndividualDatePickerVisibility(false);
  };

  // Load notes when modal becomes visible
  useEffect(() => {
    if (visible && companyId) {
      console.log('Loading notes for company:', companyId);
      console.log('Loading notes for title:', title);

      // Debug push notification state
      pushNotificationService.debugState();

      // Diagnose sound issues
      pushNotificationService.diagnoseSoundIssues();

      loadNotes(); // This will also check for completed reminders
    }
  }, [visible, companyId]);

  useEffect(() => {
    if (reminderAt && new Date() > reminderAt) {
      console.log('🔔 Reminder time has passed. Clearing it.');
      setReminderAt(null);
    }
  }, [reminderAt]);

  const loadNotes = async () => {
    setLoading(true);
    try {
      // First, clear any expired reminders from notes (keep notes)
      await noteRepository.clearExpiredReminders();

      // Then load the current notes
      const notes = await noteRepository.getNotesByCompanyId(companyId);
      setNotesList(notes);
      console.log(`Loaded ${notes.length} notes for company ${companyId}`);

      // Check for completed reminders after loading notes
      await checkAndClearCompletedReminders(notes);
    } catch (error) {
      console.error('Error loading notes:', error);
      Alert.alert('Error', 'Failed to load notes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Check for completed reminders and clear them from notes (keep notes)
  const checkAndClearCompletedReminders = async (currentNotes?: NoteData[]) => {
    try {
      const completedReminderIds =
        pushNotificationService.getAndClearCompletedReminders();

      if (completedReminderIds.length > 0) {
        console.log(
          `Found ${completedReminderIds.length} completed reminders:`,
          completedReminderIds,
        );

        // Use provided notes or current notesList
        const notesToCheck = currentNotes || notesList;
        console.log(
          `Checking ${notesToCheck.length} notes for completed reminders`,
        );

        // Find notes with completed reminders
        const notesToUpdate = notesToCheck.filter(note => {
          const hasCompletedReminder =
            note.notificationId &&
            completedReminderIds.includes(note.notificationId);
          if (hasCompletedReminder) {
            console.log(
              `Note ${note.id} has completed reminder: ${note.notificationId}`,
            );
          }
          return hasCompletedReminder;
        });

        console.log(
          `Found ${notesToUpdate.length} notes to clear reminders from`,
        );

        // Clear reminders from notes in database (keep notes)
        for (const note of notesToUpdate) {
          try {
            await noteRepository.updateNoteReminder(note.id!, null, null);
            console.log(`✅ Cleared reminder from note ${note.id} (note kept)`);
          } catch (error) {
            console.error(
              `❌ Failed to clear reminder from note ${note.id}:`,
              error,
            );
          }
        }

        // Update local state to clear reminders from notes
        if (notesToUpdate.length > 0) {
          setNotesList(prevNotes => {
            const updatedNotes = prevNotes.map(note => {
              if (
                note.notificationId &&
                completedReminderIds.includes(note.notificationId)
              ) {
                return {
                  ...note,
                  reminderAt: undefined,
                  notificationId: undefined,
                };
              }
              return note;
            });
            console.log(
              `Updated notesList: cleared reminders from ${notesToUpdate.length} notes`,
            );
            return updatedNotes;
          });

          console.log(
            `� Cleared reminders from ${notesToUpdate.length} notes (notes kept)`,
          );
        }
      } else {
        console.log('No completed reminders found');
      }
    } catch (error) {
      console.error('Error checking completed reminders:', error);
    }
  };

  const handleSave = async () => {
    if (inputValue.trim() !== '') {
      setSaving(true);
      try {
        const noteData: Omit<NoteData, 'id' | 'createdAt'> = {
          noteText: inputValue.trim(),
          companyId: companyId,
          deleted: false,
          reminderAt: reminderAt || undefined,
        };

        const noteId = await noteRepository.createNote(noteData);
        let savedNotificationId: string | undefined = undefined;

        if (noteId && reminderAt) {
          // Schedule push notification if reminder is set
          try {
            savedNotificationId = pushNotificationService.scheduleNoteReminder(
              inputValue.trim(),
              title || 'Company Note', // Use title or fallback
              reminderAt,
            );

            // Update note with notification ID
            await noteRepository.updateNoteReminder(
              noteId,
              reminderAt,
              savedNotificationId,
            );
            console.log('Note saved with reminder:', {
              noteId,
              reminderAt,
              notificationId: savedNotificationId,
            });
          } catch (notificationError) {
            console.error('Error scheduling notification:', notificationError);
            Alert.alert(
              'Note Saved',
              'Note saved successfully, but reminder notification could not be scheduled.',
            );
          }
        }

        if (noteId) {
          console.log('Note saved successfully:', noteId);
          const noteText = inputValue.trim();
          const currentReminderAt = reminderAt;

          setInputValue(''); // Clear the input field
          setReminderAt(null); // Clear reminder

          // Add the new note to the local state without reloading
          const newNote: NoteData = {
            id: noteId,
            noteText: noteText,
            companyId: companyId,
            deleted: false,
            reminderAt: currentReminderAt || undefined,
            notificationId: savedNotificationId,
            createdAt: new Date(),
          };

          setNotesList(prevNotes => [newNote, ...prevNotes]);
        } else {
          Alert.alert('Error', 'Failed to save note. Please try again.');
        }
      } catch (error) {
        console.error('Error saving note:', error);
        Alert.alert('Error', 'Failed to save note. Please try again.');
      } finally {
        setSaving(false);
      }
    }
  };

  const handleDelete = (noteId: string) => {
    Alert.alert('Delete Note', 'Are you sure you want to delete this note?', [
      {text: 'Cancel', style: 'cancel'},
      {
        text: 'Delete',
        onPress: async () => {
          try {
            // Find the note to get notification ID before deleting
            const noteToDelete = notesList.find(note => note.id === noteId);

            const success = await noteRepository.deleteNote(noteId);
            if (success) {
              // Cancel notification if it exists
              if (noteToDelete?.notificationId) {
                try {
                  pushNotificationService.cancelNotification(
                    noteToDelete.notificationId,
                  );
                  console.log(
                    'Cancelled notification:',
                    noteToDelete.notificationId,
                  );
                } catch (notificationError) {
                  console.error(
                    'Error cancelling notification:',
                    notificationError,
                  );
                }
              }
              console.log('Note deleted successfully:', noteId);

              // Remove the note from local state without reloading
              setNotesList(prevNotes =>
                prevNotes.filter(note => note.id !== noteId),
              );
            } else {
              Alert.alert('Error', 'Failed to delete note. Please try again.');
            }
          } catch (error) {
            console.error('Error deleting note:', error);
            Alert.alert('Error', 'Failed to delete note. Please try again.');
          }
        },
        style: 'destructive',
      },
    ]);
  };

  const handleConfirm = (date: Date) => {
    // Validate that the selected date is in the future
    if (!pushNotificationService.isValidReminderDate(date)) {
      Alert.alert(
        'Invalid Date',
        'Please select a future date and time for the reminder.',
        [{text: 'OK'}],
      );
      return;
    }

    setReminderAt(date);
    hideDateTimePicker();

    if (date.getTime() <= Date.now()) {
      Alert.alert('⛔ Date is in the past. Please pick a future time.');
      return;
    }

    const noteText = '📝 Reminder note'.trim();
    const companyName = title || 'Company Note';

    // Schedule notification with local time
    pushNotificationService.scheduleNoteReminder(noteText, companyName, date);

    // Show confirmation to user
    Alert.alert(
      'Reminder Set',
      `Reminder scheduled for ${pushNotificationService.formatReminderDate(
        date,
      )}`,
      [{text: 'OK'}],
    );
  };

  const handleAlarm = () => {
    const date = new Date();
    pushNotificationService.scheduleNoteReminder(
      'noteText',
      'companyName',
      date,
    );
    console.log('noteText', 'companyName', date);

    try {
      showDateTimePicker();
    } catch (error) {
      console.error('Error opening date picker:', error);
      Alert.alert('Error', 'Could not open date picker. Please try again.', [
        {text: 'OK'},
      ]);
    }
  };

  // Handler for individual note reminder confirmation
  const handleIndividualReminderConfirm = async (date: Date) => {
    if (!selectedNoteForReminder) {
      Alert.alert('Error', 'No note selected for reminder.');
      return;
    }

    // Validate that the selected date is in the future
    if (!pushNotificationService.isValidReminderDate(date)) {
      Alert.alert(
        'Invalid Date',
        'Please select a future date and time for the reminder.',
        [{text: 'OK'}],
      );
      return;
    }

    try {
      // Schedule push notification for the existing note
      const notificationId = pushNotificationService.scheduleNoteReminder(
        selectedNoteForReminder.noteText,
        title || 'Company Note',
        date,
      );

      // Update the note with reminder information
      const success = await noteRepository.updateNoteReminder(
        selectedNoteForReminder.id!,
        date,
        notificationId,
      );

      if (success) {
        console.log('Individual note reminder set:', {
          noteId: selectedNoteForReminder.id,
          reminderAt: date,
          notificationId,
        });

        // Update the note in the local state without reloading
        setNotesList(prevNotes =>
          prevNotes.map(note =>
            note.id === selectedNoteForReminder.id
              ? {...note, reminderAt: date, notificationId}
              : note,
          ),
        );

        Alert.alert(
          'Reminder Set',
          `Reminder scheduled for ${pushNotificationService.formatReminderDate(
            date,
          )}`,
          [{text: 'OK'}],
        );
      } else {
        Alert.alert('Error', 'Failed to set reminder. Please try again.');
      }
    } catch (error) {
      console.error('Error setting individual note reminder:', error);
      Alert.alert('Error', 'Failed to set reminder. Please try again.');
    } finally {
      hideIndividualDateTimePicker();
    }
  };

  // Handler for removing individual note reminder
  const handleRemoveIndividualReminder = async (note: NoteData) => {
    if (!note.reminderAt) {
      Alert.alert('Info', 'This note does not have a reminder set.');
      return;
    }

    Alert.alert(
      'Remove Reminder',
      'Are you sure you want to remove the reminder for this note?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              // Cancel the notification if it exists
              if (note.notificationId) {
                pushNotificationService.cancelNotification(note.notificationId);
              }

              // Update the note to remove reminder
              const success = await noteRepository.updateNoteReminder(
                note.id!,
                null,
                null,
              );

              if (success) {
                console.log('Individual note reminder removed:', note.id);

                // Update the note in the local state without reloading
                setNotesList(prevNotes =>
                  prevNotes.map(n =>
                    n.id === note.id
                      ? {...n, reminderAt: undefined, notificationId: undefined}
                      : n,
                  ),
                );
              } else {
                Alert.alert(
                  'Error',
                  'Failed to remove reminder. Please try again.',
                );
              }
            } catch (error) {
              console.error('Error removing individual note reminder:', error);
              Alert.alert(
                'Error',
                'Failed to remove reminder. Please try again.',
              );
            }
          },
        },
      ],
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.topHeaderView}>
            <View>
              <Text> </Text>
            </View>
            {/*
            <TouchableOpacity
              style={{
                height: 40,
                flexDirection: 'row',
                justifyContent: 'flex-start',
                alignItems: 'center',
                padding: 5,
                borderColor: '#ff6b6b',
                borderWidth: 1,
                borderRadius: 6,
              }}
              onPress={handleAlarm}>
              <Image
                style={{
                  marginLeft: 3,
                  height: 22,
                  width: 22,
                  marginRight: 5,
                  tintColor: '#ff6b6b',
                }}
                source={Images.ic_bell}
              />
              <Text
                style={{
                  color: '#ff6b6b',
                  marginLeft: 1,
                  marginRight: 4,
                  fontFamily: FONTS.POPPINS.MEDIUM,
                  fontSize: 16,
                  paddingTop: 4,
                }}>
                Set Reminder
              </Text>
            </TouchableOpacity> */}
            <TouchableOpacity
              style={{height: 44, alignItems: 'flex-end'}}
              onPress={onClose}>
              <Image style={styles.closeIcon} source={Images.ic_close} />
            </TouchableOpacity>
          </View>
          {reminderAt && (
            <View style={styles.reminderInfo}>
              <Text style={styles.reminderText}>
                📅 Reminder:{' '}
                {pushNotificationService.formatReminderDate(reminderAt)}
              </Text>
              <TouchableOpacity
                onPress={() => setReminderAt(null)}
                style={styles.clearReminderButton}>
                <Text style={styles.clearReminderText}>Clear</Text>
              </TouchableOpacity>
            </View>
          )}
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#0a1d50" />
              <Text style={styles.loadingText}>Loading notes...</Text>
            </View>
          ) : (
            <FlatList
              data={notesList}
              keyExtractor={item => item.id || item.noteId || ''}
              renderItem={({item}) => (
                <View style={styles.itemContainer}>
                  <View style={styles.noteContent}>
                    <Text style={styles.itemText}>{item.noteText}</Text>
                    {item.reminderAt && (
                      <Text style={styles.reminderIndicator}>
                        🔔{' '}
                        {pushNotificationService.formatReminderDate(
                          item.reminderAt,
                        )}
                      </Text>
                    )}
                  </View>

                  {/* Reminder button - shows different states based on whether reminder exists */}
                  <TouchableOpacity
                    onPress={() =>
                      item.reminderAt
                        ? handleRemoveIndividualReminder(item)
                        : showIndividualDateTimePicker(item)
                    }
                    style={[
                      styles.actionButton,
                      item.reminderAt
                        ? styles.reminderActiveButton
                        : styles.reminderInactiveButton,
                    ]}>
                    <Image
                      source={Images.ic_bell}
                      style={[
                        styles.actionIcon,
                        {tintColor: item.reminderAt ? '#ff6b6b' : '#0a1d50'},
                      ]}
                    />
                  </TouchableOpacity>

                  {/* Delete button */}
                  <TouchableOpacity
                    onPress={() => handleDelete(item.id || '')}
                    style={[styles.actionButton, styles.deleteActionButton]}>
                    <Image
                      source={Images.ic_delete}
                      style={[styles.actionIcon]}
                    />
                  </TouchableOpacity>
                </View>
              )}
              style={{padding: 2}}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    No notes yet. Add your first note below!
                  </Text>
                </View>
              }
            />
          )}
          <View style={commonStyles.textHereInputView}>
            <Text style={commonStyles.textHereInputHeader}>Note</Text>
            <TextInput
              style={commonStyles.textHereView}
              multiline
              placeholder="text here..."
              placeholderTextColor={'#B7B7B7'}
              value={inputValue}
              onChangeText={setInputValue}
              textAlignVertical="top"
              autoCorrect={false}
              spellCheck={false}
            />
            <Button
              mode="contained"
              onPress={handleSave}
              style={commonStyles.bottomButton}
              labelStyle={commonStyles.bottomButtonLabel}
              buttonColor="#0a1d50"
              disabled={saving || inputValue.trim() === ''}>
              {saving ? 'Saving...' : 'Save'}
            </Button>
          </View>
        </View>
      </View>
      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        mode="datetime"
        date={reminderAt || new Date()}
        onConfirm={handleConfirm}
        onCancel={hideDateTimePicker}
        display="default" // can be 'spinner', 'inline', 'calendar'
        isDarkModeEnabled={false}
      />

      {/* Second DateTimePickerModal for individual note reminders */}
      <DateTimePickerModal
        isVisible={isIndividualDatePickerVisible}
        mode="datetime"
        date={selectedNoteForReminder?.reminderAt || new Date()}
        onConfirm={handleIndividualReminderConfirm}
        onCancel={hideIndividualDateTimePicker}
        display="default"
        isDarkModeEnabled={false}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    maxHeight: '95%',
  },
  itemTitle: {
    fontSize: 20,
    fontFamily: FONTS.POPPINS.MEDIUM,
    marginBottom: 5,
    marginTop: 5,
  },
  itemText: {
    fontSize: 15,
    fontFamily: FONTS.POPPINS.REGULAR,
    textAlign: 'left',
    flex: 1,
  },
  itemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 10,
    borderWidth: 1,
    borderColor: COLORS.BORDER_COLOR,
    marginBottom: 15,
    borderRadius: 10,
    alignItems: 'center',
    gap: 10,
  },
  topHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 10,
  },
  closeIcon: {
    marginLeft: 3,
    height: 30,
    width: 30,
    marginRight: 5,
  },
  deleteButton: {
    paddingHorizontal: 2,
    paddingVertical: 4,
    width: 30,
    height: 30,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    fontFamily: FONTS.POPPINS.REGULAR,
    color: '#666',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 14,
    fontFamily: FONTS.POPPINS.REGULAR,
    color: '#999',
    textAlign: 'center',
  },
  reminderInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    padding: 10,
    borderRadius: 8,
    marginVertical: 10,
    borderWidth: 1,
    borderColor: '#0a1d50',
  },
  reminderText: {
    fontSize: 12,
    fontFamily: FONTS.POPPINS.REGULAR,
    color: '#0a1d50',
    flex: 1,
  },
  clearReminderButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#ff6b6b',
    borderRadius: 4,
  },
  clearReminderText: {
    fontSize: 10,
    fontFamily: FONTS.POPPINS.MEDIUM,
    color: 'white',
  },
  noteContent: {
    flex: 1,
    marginRight: 10,
  },
  reminderIndicator: {
    fontSize: 10,
    fontFamily: FONTS.POPPINS.REGULAR,
    color: '#0a1d50',
    marginTop: 4,
    fontStyle: 'italic',
  },
  actionButton: {
    padding: 8,
    borderRadius: 6,
    marginLeft: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionIcon: {
    height: 20,
    width: 20,
  },
  reminderActiveButton: {
    backgroundColor: '#ffe6e6',
    borderWidth: 1,
    borderColor: '#ff6b6b',
  },
  reminderInactiveButton: {
    backgroundColor: '#f0f8ff',
    borderWidth: 1,
    borderColor: '#0a1d50',
  },
  deleteActionButton: {
    borderWidth: 1,
    borderColor: '#ff4444',
  },
});

export default NoteModalScreen;
