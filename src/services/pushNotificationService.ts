import {Platform, Alert, PermissionsAndroid} from 'react-native';
import PushNotification from 'react-native-push-notification';

class PushNotificationService {
  private isInitialized = false;
  private completedReminders: Set<string> = new Set(); // Track completed reminder notifications

  /**
   * Initialize local notification service for Android (no Firebase/remote notifications)
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('[PushNotification] Already initialized');
      return;
    }

    if (Platform.OS !== 'android') {
      console.log('[PushNotification] iOS not supported in this app');
      return;
    }

    try {
      console.log('[PushNotification] Initializing for Android...');

      // Request permissions first
      const hasPermissions = await this.requestAndroidPermissions();
      if (!hasPermissions) {
        console.warn(
          '[PushNotification] Notification permissions not granted, but continuing with initialization',
        );
      }

      // Configure local notifications (no Firebase/remote notifications)
      PushNotification.configure({
        // Called when token is generated (not used for local notifications)
        onRegister: function () {
          console.log(
            '[PushNotification] Local notification service registered',
          );
        },

        // Called when a remote or local notification is opened or received
        onNotification: function (notification: any) {
          console.log('[PushNotification] NOTIFICATION received:', {
            id: notification.id,
            title: notification.title,
            message: notification.message,
            userInteraction: notification.userInteraction,
            data: notification.data,
            foreground: notification.foreground,
          });

          // Handle notification tap or automatic trigger
          if (notification.userInteraction) {
            console.log('[PushNotification] User tapped notification');
            // You can add navigation logic here if needed
          }

          // Handle note reminder completion - check both data and userInfo
          const isNoteReminder =
            (notification.data && notification.data.type === 'note_reminder') ||
            (notification.userInfo &&
              notification.userInfo.type === 'note_reminder');

          if (isNoteReminder) {
            const notificationId =
              notification.id ||
              notification.data?.notificationId ||
              notification.userInfo?.notificationId;

            console.log(
              '[PushNotification] Note reminder triggered, marking for removal:',
              notificationId,
            );

            if (notificationId) {
              pushNotificationService.handleReminderCompletion(notificationId);
            }
          }
        },

        // Called when the user fails to register for remote notifications
        onRegistrationError: function (err) {
          console.error(
            '[PushNotification] Registration error:',
            err.message,
            err,
          );
        },

        // Should the initial notification be popped automatically
        popInitialNotification: true,

        // Request permissions for local notifications only (no remote/Firebase)
        requestPermissions: false, // Disable automatic permission request to avoid Firebase
      });

      // Create notification channel for Android
      if (Platform.OS === 'android') {
        PushNotification.createChannel(
          {
            channelId: 'note-reminders', // (required)
            channelName: 'Note Reminders', // (required)
            channelDescription: 'Notifications for note reminders', // (optional) default: undefined.
            playSound: true, // (optional) default: true
            soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
            importance: 4, // (optional) default: 4. Int value of the Android notification importance
            vibrate: true, // (optional) default: true. Creates the default vibration pattern if true.
            vibration: 1000, // vibration length in milliseconds
            enableLights: true, // Enable LED lights
            enableVibration: true, // Enable vibration
            showBadge: true, // Show badge on app icon
          },
          created => {
            console.log(`[PushNotification] Channel created: ${created}`);
            if (created) {
              console.log(
                '[PushNotification] New channel created successfully with sound enabled',
              );
            } else {
              console.log(
                '[PushNotification] Channel already exists - sound settings may need manual reset',
              );
            }
          },
        );
      }

      this.isInitialized = true;
      console.log('[PushNotification] Initialized successfully');
    } catch (error) {
      console.error('[PushNotification] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Schedule a local notification for a note reminder
   */
  scheduleNoteReminder(
    noteText: string,
    companyName: string,
    reminderDate: Date,
  ): string {
    if (!this.isInitialized) {
      throw new Error('Push notification service not initialized');
    }

    if (Platform.OS !== 'android') {
      throw new Error('Push notifications only supported on Android');
    }

    // Generate unique notification ID
    const notificationId = `note_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;

    try {
      console.log('[PushNotification] Scheduling reminder:', {
        id: notificationId,
        noteText: noteText.substring(0, 50) + '...',
        companyName,
        reminderDate: reminderDate,
      });
      const selectedDate = reminderDate;
      PushNotification.localNotificationSchedule({
        id: notificationId, // Add explicit ID
        channelId: 'note-reminders',
        title: `Note Reminder: ${companyName}`,
        message:
          noteText.length > 100 ? noteText.substring(0, 100) + '...' : noteText,
        date: selectedDate,
        allowWhileIdle: true, // Allow notification even when device is in doze mode
        repeatType: undefined, // No repeat
        actions: ['View Note'],
        invokeApp: true, // Open app when notification is tapped
        userInfo: {
          type: 'note_reminder',
          noteText,
          companyName,
          notificationId,
        },
        data: {
          type: 'note_reminder',
          notificationId,
          noteText,
          companyName,
        },
        // Android specific - Enhanced sound settings
        largeIcon: 'ic_launcher', // Use app icon
        smallIcon: 'ic_notification', // You may need to add this icon
        bigText: noteText, // Show full text in expanded notification
        subText: `Reminder for ${companyName}`,
        vibrate: true,
        vibration: 1000, // Longer vibration
        playSound: true,
        soundName: 'default', // Use system default sound
        importance: 'high', // High importance for sound
        priority: 'high', // High priority
        autoCancel: true, // Auto dismiss when tapped
        ongoing: false, // Not persistent
        when: selectedDate.getTime(), // Explicit timestamp
        usesChronometer: false,
        timeoutAfter: null, // Don't auto-dismiss
      });

      console.log(
        `[PushNotification] Scheduled notification ${notificationId} for ${reminderDate}`,
      );
      return notificationId;
    } catch (error) {
      console.error(
        '[PushNotification] Failed to schedule notification:',
        error,
      );
      throw error;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  cancelNotification(notificationId: string): void {
    if (!this.isInitialized) {
      console.warn(
        '[PushNotification] Service not initialized, cannot cancel notification',
      );
      return;
    }

    if (Platform.OS !== 'android') {
      console.warn('[PushNotification] Cancel only supported on Android');
      return;
    }

    try {
      console.log(
        `[PushNotification] Cancelling notification: ${notificationId}`,
      );
      PushNotification.cancelLocalNotification(notificationId);
      console.log(
        `[PushNotification] Cancelled notification: ${notificationId}`,
      );
    } catch (error) {
      console.error('[PushNotification] Failed to cancel notification:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  cancelAllNotifications(): void {
    if (!this.isInitialized) {
      console.warn(
        '[PushNotification] Service not initialized, cannot cancel notifications',
      );
      return;
    }

    if (Platform.OS !== 'android') {
      console.warn('[PushNotification] Cancel all only supported on Android');
      return;
    }

    try {
      console.log('[PushNotification] Cancelling all notifications');
      PushNotification.cancelAllLocalNotifications();
      console.log('[PushNotification] Cancelled all notifications');
    } catch (error) {
      console.error(
        '[PushNotification] Failed to cancel all notifications:',
        error,
      );
    }
  }

  /**
   * Check if a date is valid for scheduling (must be in the future)
   */
  isValidReminderDate(date: Date): boolean {
    const now = new Date();
    return date > now;
  }

  /**
   * Get formatted date string for display
   */
  formatReminderDate(date: Date): string {
    return date.toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  }

  /**
   * Handle reminder completion when notification is triggered
   */
  handleReminderCompletion(notificationId: string): void {
    console.log(
      `[PushNotification] Marking reminder as completed: ${notificationId}`,
    );
    this.completedReminders.add(notificationId);
  }

  /**
   * Check if a reminder has been completed
   */
  isReminderCompleted(notificationId: string): boolean {
    return this.completedReminders.has(notificationId);
  }

  /**
   * Get all completed reminder IDs and clear the list
   */
  getAndClearCompletedReminders(): string[] {
    const completed = Array.from(this.completedReminders);
    this.completedReminders.clear();
    console.log(
      `[PushNotification] Retrieved ${completed.length} completed reminders:`,
      completed,
    );
    return completed;
  }

  /**
   * Remove a specific completed reminder from tracking
   */
  removeCompletedReminder(notificationId: string): void {
    this.completedReminders.delete(notificationId);
  }

  /**
   * Get current completed reminders (for debugging)
   */
  getCompletedReminders(): string[] {
    return Array.from(this.completedReminders);
  }

  /**
   * Debug function to log current state
   */
  debugState(): void {
    console.log('[PushNotification] Debug State:', {
      isInitialized: this.isInitialized,
      completedReminders: Array.from(this.completedReminders),
      completedCount: this.completedReminders.size,
    });
  }

  /**
   * Test function to manually mark a reminder as completed (for debugging)
   */
  testMarkReminderCompleted(notificationId: string): void {
    console.log(
      `[PushNotification] TEST: Manually marking reminder as completed: ${notificationId}`,
    );
    this.handleReminderCompletion(notificationId);
    this.debugState();
  }

  /**
   * Clear all completed reminders (for testing/debugging)
   */
  clearAllCompletedReminders(): void {
    console.log('[PushNotification] Clearing all completed reminders');
    this.completedReminders.clear();
    this.debugState();
  }

  /**
   * Test notification functionality (for debugging)
   */
  testNotification(): void {
    if (!this.isInitialized) {
      console.warn('[PushNotification] Service not initialized');
      return;
    }

    if (Platform.OS !== 'android') {
      console.warn('[PushNotification] Test only supported on Android');
      return;
    }

    try {
      console.log('[PushNotification] Sending test notification...');

      PushNotification.localNotification({
        id: 'test_notification',
        channelId: 'note-reminders',
        title: 'Test Notification',
        message: 'Push notification service is working correctly!',
        playSound: true,
        soundName: 'default',
        importance: 'high',
        priority: 'high',
        largeIcon: 'ic_launcher',
        smallIcon: 'ic_notification',
        vibrate: true,
        vibration: 1000, // Longer vibration for testing
        autoCancel: true,
        invokeApp: true,
      });

      console.log('[PushNotification] Test notification sent');
    } catch (error) {
      console.error(
        '[PushNotification] Failed to send test notification:',
        error,
      );
    }
  }

  /**
   * Diagnose notification sound issues
   */
  diagnoseSoundIssues(): void {
    console.log('[PushNotification] === SOUND DIAGNOSIS ===');
    console.log('[PushNotification] Service initialized:', this.isInitialized);
    console.log('[PushNotification] Platform:', Platform.OS);

    if (Platform.OS === 'android') {
      console.log('[PushNotification] Android notification channel settings:');
      console.log('  - Channel ID: note-reminders');
      console.log('  - Play Sound: true');
      console.log('  - Sound Name: default');
      console.log('  - Importance: 4 (HIGH)');
      console.log('  - Vibrate: true');

      console.log('[PushNotification] Common sound issues:');
      console.log('  1. Device is in silent/DND mode');
      console.log('  2. App notification sound is disabled in system settings');
      console.log(
        '  3. Notification channel sound was disabled after creation',
      );
      console.log('  4. Emulator sound is muted');

      console.log('[PushNotification] To fix:');
      console.log('  1. Check device sound settings');
      console.log(
        '  2. Go to Settings > Apps > IndiaCustomerCare > Notifications',
      );
      console.log('  3. Ensure "Note Reminders" channel has sound enabled');
      console.log('  4. Clear app data to recreate notification channel');
    }
    console.log('[PushNotification] === END DIAGNOSIS ===');
  }

  /**
   * Force recreate notification channel with sound enabled
   * Use this if sound stops working after channel creation
   */
  recreateNotificationChannel(): void {
    if (Platform.OS !== 'android') {
      console.warn(
        '[PushNotification] Channel recreation only supported on Android',
      );
      return;
    }

    try {
      console.log(
        '[PushNotification] Recreating notification channel with sound...',
      );

      // Delete existing channel first (if possible)
      PushNotification.deleteChannel('note-reminders');

      // Wait a moment then recreate
      setTimeout(() => {
        PushNotification.createChannel(
          {
            channelId: 'note-reminders',
            channelName: 'Note Reminders',
            channelDescription: 'Notifications for note reminders with sound',
            playSound: true,
            soundName: 'default',
            importance: 4,
            vibrate: true,
            vibration: 1000,
            enableLights: true,
            enableVibration: true,
            showBadge: true,
          },
          created => {
            console.log(`[PushNotification] Channel recreated: ${created}`);
            if (created) {
              console.log(
                '[PushNotification] ✅ New channel created with sound enabled',
              );
              // Test the new channel immediately
              this.testImmediateNotification();
            } else {
              console.log(
                '[PushNotification] ⚠️ Channel may still exist - try clearing app data',
              );
            }
          },
        );
      }, 500);
    } catch (error) {
      console.error('[PushNotification] Failed to recreate channel:', error);
    }
  }

  /**
   * Test immediate local notification (no Firebase/remote dependencies)
   */
  testImmediateNotification(): void {
    if (!this.isInitialized) {
      console.warn('[PushNotification] Service not initialized');
      return;
    }

    if (Platform.OS !== 'android') {
      console.warn('[PushNotification] Test only supported on Android');
      return;
    }

    try {
      console.log(
        '[PushNotification] Sending immediate LOCAL notification (no Firebase)...',
      );

      // Send local notification immediately (no remote/Firebase dependencies)
      PushNotification.localNotification({
        id: 'test_immediate_local',
        channelId: 'note-reminders',
        title: 'Local Notification Test',
        message: 'This is a LOCAL notification (no Firebase required)!',
        playSound: true,
        soundName: 'default',
        importance: 'high',
        priority: 'high',
        largeIcon: 'ic_launcher',
        smallIcon: 'ic_notification',
        vibrate: true,
        vibration: 300,
        autoCancel: true,
        invokeApp: true,
      });

      console.log(
        '[PushNotification] Immediate LOCAL notification sent successfully',
      );
    } catch (error) {
      console.error(
        '[PushNotification] Failed to send immediate local notification:',
        error,
      );
    }
  }

  /**
   * Test scheduled notification for 5 seconds from now (for debugging)
   */
  testScheduledNotification(): void {
    if (!this.isInitialized) {
      console.warn('[PushNotification] Service not initialized');
      return;
    }

    if (Platform.OS !== 'android') {
      console.warn('[PushNotification] Test only supported on Android');
      return;
    }

    try {
      const testDate = new Date(Date.now() + 5000); // 5 seconds from now
      console.log(
        '[PushNotification] Scheduling test notification for:',
        testDate.toISOString(),
      );

      PushNotification.localNotificationSchedule({
        id: 'test_scheduled',
        channelId: 'note-reminders',
        title: 'Scheduled Test',
        message: 'This notification was scheduled 5 seconds ago!',
        date: testDate,
        playSound: true,
        soundName: 'default',
        importance: 'high',
        priority: 'high',
        largeIcon: 'ic_launcher',
        smallIcon: 'ic_notification',
        vibrate: true,
        vibration: 300,
        autoCancel: true,
        invokeApp: true,
        allowWhileIdle: true,
      });

      console.log(
        '[PushNotification] Scheduled test notification for 5 seconds from now',
      );
    } catch (error) {
      console.error(
        '[PushNotification] Failed to schedule test notification:',
        error,
      );
    }
  }

  /**
   * Request Android notification permissions
   */
  async requestAndroidPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      console.warn(
        '[PushNotification] Android permissions only needed on Android',
      );
      return true;
    }

    try {
      console.log(
        '[PushNotification] Requesting Android notification permissions...',
      );
      console.log('Platform.Version --', Platform.Version);

      // For Android 13+ (API 33+), we need to request POST_NOTIFICATIONS permission
      if (Platform.Version >= 33) {
        console.log(
          '[PushNotification] Android 13+ detected, requesting POST_NOTIFICATIONS permission...',
        );

        // Check if permission is already granted
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        console.log(
          '[PushNotification] Current permission status:',
          hasPermission,
        );

        if (hasPermission) {
          console.log(
            '[PushNotification] POST_NOTIFICATIONS permission already granted',
          );
          return true;
        }

        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          {
            title: 'Enable Notifications',
            message:
              'This app needs notification permission to send you note reminders and alerts. Please tap "Allow" to enable notifications.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: "Don't Allow",
            buttonPositive: 'Allow',
          },
        );

        console.log('[PushNotification] Permission request result:', granted);
        const isGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
        console.log(
          '[PushNotification] POST_NOTIFICATIONS permission:',
          isGranted ? 'granted' : 'denied',
        );

        if (!isGranted) {
          console.warn(
            '[PushNotification] Permission denied. User needs to enable notifications manually in Settings.',
          );
        }

        return isGranted;
      }

      // For older Android versions, permissions are granted at install time
      console.log(
        '[PushNotification] Android version < 33, permissions granted at install time',
      );
      return true;
    } catch (error) {
      console.error('[PushNotification] Failed to request permissions:', error);
      return false;
    }
  }

  /**
   * Check notification permissions and settings
   */
  async checkNotificationPermissions(): Promise<void> {
    if (Platform.OS !== 'android') {
      console.warn(
        '[PushNotification] Permission check only supported on Android',
      );
      return;
    }

    try {
      console.log('[PushNotification] Checking notification permissions...');

      // Check if notifications are enabled
      PushNotification.checkPermissions(permissions => {
        console.log('[PushNotification] Current permissions:', permissions);
      });

      // Get all scheduled notifications
      PushNotification.getScheduledLocalNotifications(notifications => {
        console.log(
          '[PushNotification] Scheduled notifications:',
          notifications,
        );
      });

      // Get delivered notifications
      PushNotification.getDeliveredNotifications(notifications => {
        console.log(
          '[PushNotification] Delivered notifications:',
          notifications,
        );
      });
    } catch (error) {
      console.error('[PushNotification] Failed to check permissions:', error);
    }
  }

  /**
   * Simple local notification test (minimal setup required)
   */
  testSimpleLocalNotification(): void {
    if (Platform.OS !== 'android') {
      console.warn('[PushNotification] Simple test only supported on Android');
      return;
    }

    try {
      console.log('[PushNotification] Sending SIMPLE local notification...');

      // Most basic local notification possible
      PushNotification.localNotification({
        title: 'Simple Test',
        message: 'Basic local notification working!',
      });

      console.log('[PushNotification] Simple local notification sent');
    } catch (error) {
      console.error(
        '[PushNotification] Failed to send simple notification:',
        error,
      );
    }
  }
}

const pushNotificationService = new PushNotificationService();
export default pushNotificationService;
